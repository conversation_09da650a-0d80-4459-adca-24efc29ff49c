const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const contactStatusService = require("./contact-statuses.service");

// Routes
router.get("/", getAll);
router.post("/", contactStatusSchema, create);
router.get("/:id", getById);
router.put("/:id", contactStatusSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  contactStatusService
    .create(req.body)
    .then((contactStatus) => {
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  contactStatusService
    .getAll(req.query)
    .then((contactStatuses) => res.json(contactStatuses))
    .catch(next);
}

function getById(req, res, next) {
  contactStatusService
    .getById(req.params.id)
    .then((contactStatus) => res.json(contactStatus))
    .catch(next);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  contactStatusService
    .update(req.params.id, req.body)
    .then((contactStatus) => {
      res.json({ status: true, message: "Record updated successfully" });
    })
    .catch(next);
}

function _delete(req, res, next) {
  const userId = req?.headers?.userid;

  contactStatusService
    ._delete(req.params.id, userId)
    .then(() => {
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function contactStatusSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    slug: Joi.string().optional(), // Auto-generated, so optional
    status: Joi.string().optional(),
    createdBy: Joi.number().integer().optional(),
    updatedBy: Joi.number().integer().optional(),
    deletedBy: Joi.number().integer().optional(),
    deletedAt: Joi.date().optional(),
  });
  validateRequest(req, next, schema);
}
