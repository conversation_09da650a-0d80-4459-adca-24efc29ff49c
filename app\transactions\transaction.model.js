const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    customerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "customers",
        key: "id",
      },
    },
    customerEmployeeId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "customer_employees",
        key: "id",
      },
    },
    mediumId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "media",
        key: "id",
      },
    },
    channelId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "channels",
        key: "id",
      },
    },
    propertyId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "properties",
        key: "id",
      },
    },
    variantId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "variants",
        key: "id",
      },
    },
    cityId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "cities",
        key: "id",
      },
    },

    paymentStatus: {
      type: DataTypes.ENUM("pending", "partial", "paid", "cancelled"),
      defaultValue: "pending",
    },
    paymentDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    paymentMethod: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    paymentReference: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    invoiceNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    invoiceDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    poNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
    },
    month: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1900,
        max: 2100,
      },
    },
    revenue: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM("active", "inactive", "cancelled"),
      defaultValue: "active",
    },
    stage: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    attachments: {
      type: DataTypes.TEXT,
      allowNull: true,
      get() {
        const value = this.getDataValue("attachments");
        return value ? JSON.parse(value) : [];
      },
      set(value) {
        this.setDataValue("attachments", JSON.stringify(value));
      },
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    deletedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  };

  const options = {
    defaultScope: {
      // add default scope options if needed
    },
    scopes: {
      withDetails: { attributes: {} },
    },
  };

  return sequelize.define("transactions", attributes, options);
}
