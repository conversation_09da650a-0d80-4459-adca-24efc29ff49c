const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  const whereClause = {
    deletedAt: null, // Exclude soft-deleted records
  };

  // Add additional filters if provided
  if (params?.status) {
    whereClause.status = params.status;
  }

  return await db.City.findAll({
    where: whereClause,
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Generate slug from name
  params.slug = utils.generateSlug(params.name);
  // Generate code from name
  params.code = utils.generateSlug(params.name);
  // Set default status if not provided
  if (!params.status) params.status = "active";
  // Set audit fields
  params.createdBy = params.userId || null;

  const record = await db.City.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Update slug and code if name is changed
  if (params.name && params.name !== record.name) {
    params.slug = utils.generateSlug(params.name);
    params.code = utils.generateSlug(params.name);
  }

  // Set audit fields
  params.updatedBy = params.userId || null;

  // copy params to City and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id, userId) {
  const record = await getSingleRecord(id);

  // Soft delete
  record.deletedBy = userId || null;
  record.deletedAt = new Date();
  await record.save();

  // Or hard delete if preferred
  // await record.destroy();
}

async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Collect city names from Excel and generate slugs
    const cityNamesInSheet = rows
      .map((row) => row.Name || row.name || row.CityName || row["City Name"])
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const slugsInSheet = cityNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Preload existing cities by slug to check for duplicates
    const existingCities = await db.City.findAll({
      where: {
        slug: slugsInSheet,
        deletedAt: null,
      },
      attributes: ["id", "slug", "name"],
    });

    const existingSlugMap = new Map();
    existingCities.forEach((city) => {
      existingSlugMap.set(city.slug, city);
    });

    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Process each row
    rows.forEach((row, index) => {
      const name = (row.Name || row.name || row.CityName || row["City Name"])
        ?.toString()
        .trim();
      const state = (row.State || row.state)?.toString().trim();
      const country =
        (row.Country || row.country)?.toString().trim() || "India";

      if (!name) {
        errorLog.push({
          row: index + 2,
          reason: "Missing City Name",
          data: row,
        });
        return;
      }

      const slug = utils.generateSlug(name);

      // Check for duplicate by slug
      if (existingSlugMap.has(slug)) {
        skippedList.push({
          row: index + 2,
          name: name,
          reason: `Duplicate city found: ${existingSlugMap.get(slug).name}`,
          existingCity: existingSlugMap.get(slug),
        });
        return;
      }

      // Prepare city data for insertion
      const cityData = {
        name: name,
        slug: slug,
        code: slug,
        state: state || null,
        country: country,
        status: "active",
        createdBy: userId || null,
      };

      insertList.push(cityData);
      // Add to existing map to prevent duplicates within the same file
      existingSlugMap.set(slug, { name: name, slug: slug });
    });

    // Perform bulk insert using transaction
    let insertedCities = [];
    if (insertList.length > 0) {
      const transaction = await db.sequelize.transaction();
      try {
        insertedCities = await db.City.bulkCreate(insertList, {
          transaction,
          validate: true,
        });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw new Error(`Bulk insert failed: ${error.message}`);
      }
    }

    // Create rejected rows file (skipped + errors) with reasons
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return detailed results
    // Return simplified results for consistent frontend handling
    return {
      totalRows: rows.length,
      insertedCount: insertedCities.length,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertedCities,
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    // Create a map of rejected rows by row number for quick lookup
    const rejectedRowsMap = new Map();
    rejectedRows.forEach((rejected) => {
      rejectedRowsMap.set(rejected.row, rejected.reason);
    });

    // Filter original rows to get only rejected ones and add reason column
    const rejectedRowsWithReasons = [];

    originalRows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)
      if (rejectedRowsMap.has(rowNumber)) {
        // Add the reason column to the original row data
        const rowWithReason = {
          ...row,
          "Rejection Reason": rejectedRowsMap.get(rowNumber),
        };
        rejectedRowsWithReasons.push(rowWithReason);
      }
    });

    if (rejectedRowsWithReasons.length === 0) {
      return null;
    }

    // Generate unique filename for rejected rows
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const originalFileName = path.basename(
      originalFilePath,
      path.extname(originalFilePath)
    );
    const rejectedFileName = `${originalFileName}_rejected_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Create new workbook with rejected rows
    const workbook = xlsx.utils.book_new();
    const worksheet = xlsx.utils.json_to_sheet(rejectedRowsWithReasons);
    xlsx.utils.book_append_sheet(workbook, worksheet, "Rejected Rows");

    // Write the rejected rows file
    xlsx.writeFile(workbook, rejectedFilePath);

    // Return the download URL (relative path from uploads directory with /api prefix)
    const relativePath = path.relative(__basedir + "uploads", rejectedFilePath);
    return `/uploads/${relativePath.replace(/\\/g, "/")}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// helper function
async function getSingleRecord(id) {
  const record = await db.City.findOne({
    where: {
      id: id,
      deletedAt: null, // Exclude soft-deleted records
    },
  });
  if (!record) throw "Record not found";
  return record;
}
