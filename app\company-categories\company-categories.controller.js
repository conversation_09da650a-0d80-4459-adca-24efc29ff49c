const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const companyCategoryService = require("./company-categories.service");

// Routes
router.get("/", getAll);
router.post("/", companyCategorySchema, create);
router.get("/:id", getById);
router.put("/:id", companyCategorySchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  companyCategoryService
    .create(req.body)
    .then((companyCategory) => {
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  companyCategoryService
    .getAll(req.query)
    .then((companyCategories) => res.json(companyCategories))
    .catch(next);
}

function getById(req, res, next) {
  companyCategoryService
    .getById(req.params.id)
    .then((companyCategory) => res.json(companyCategory))
    .catch(next);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  companyCategoryService
    .update(req.params.id, req.body)
    .then((companyCategory) => {
      res.json({ status: true, message: "Record updated successfully" });
    })
    .catch(next);
}

function _delete(req, res, next) {
  const userId = req?.headers?.userid;

  companyCategoryService
    ._delete(req.params.id, userId)
    .then(() => {
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function companyCategorySchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    slug: Joi.string().optional(), // Auto-generated, so optional
    status: Joi.string().optional(),
    createdBy: Joi.number().integer().optional(),
    updatedBy: Joi.number().integer().optional(),
    deletedBy: Joi.number().integer().optional(),
    deletedAt: Joi.date().optional(),
  });
  validateRequest(req, next, schema);
}
