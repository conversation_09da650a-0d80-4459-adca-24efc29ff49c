const express = require("express");
const app = express();
const cors = require("cors");
const bodyParser = require("body-parser");
const Razorpay = require("razorpay");
const crypto = require("crypto");
const config = require("./config.json");

const path = require("path");
const errorHandler = require("./app/_middleware/error-handler");
global.__basedir = __dirname + "/";
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));
// app.use(cors());
const corsOptions = {
  origin:
    config.environment === "FAT"
      ? "http://localhost:5173"
      : "https://mediafrontend.interosys.in/", // ✅ or your frontend domain
  credentials: true, // ✅ required if you're using cookies or Authorization headers
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"], // ✅ adjust based on headers you use
};

app.use(cors(corsOptions));
app.options("*", cors(corsOptions));

app.use("/api/contacts", require("./app/contacts/contacts.controller"));
app.use("/api/channels", require("./app/channels/channels.controller"));
app.use("/api/cities", require("./app/cities/cities.controller"));
app.use("/api/companies", require("./app/companies/companies.controller"));
app.use("/api/customers", require("./app/customers/customers.controller"));
app.use(
  "/api/customer-employees",
  require("./app/customer-employees/customer-employees.controller")
);
app.use("/api/mediums", require("./app/mediums/mediums.controller"));
app.use("/api/properties", require("./app/properties/properties.controller"));
app.use(
  "/api/transactions",
  require("./app/transactions/transactions.controller")
);
app.use("/api/users", require("./app/users/users.controller"));
app.use("/api/variants", require("./app/variants/variants.controller"));
app.use("/api/stages", require("./app/stages/stages.controller"));
app.use(
  "/api/company-categories",
  require("./app/company-categories/company-categories.controller")
);
app.use(
  "/api/contact-statuses",
  require("./app/contact-statuses/contact-statuses.controller")
);

// api routes
app.get("/", (req, res) => {
  res.json({ message: "Welcome to application." });
});

// Serve the uploads folder to access files by URL
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// global error handler
app.use(errorHandler);

// start server
const port =
  process.env.NODE_ENV === "production" ? process.env.PORT || 80 : 4000;
app.listen(port, () => console.log("Server listening on port " + port));
