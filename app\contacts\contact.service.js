const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  const whereClause = {};

  // Add filters if provided
  if (params?.status) {
    whereClause.status = params.status;
  }
  if (params?.priority) {
    whereClause.priority = params.priority;
  }
  if (params?.assignedTo) {
    whereClause.assignedTo = params.assignedTo;
  }

  return await db.Contact.findAll({
    where: whereClause,
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Set default values if not provided
  if (!params.status) params.status = "new";
  if (!params.priority) params.priority = "medium";

  const record = await db.Contact.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to Contact and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Collect contact emails from Excel for duplicate checking
    const contactEmailsInSheet = rows
      .map(
        (row) =>
          row.Email ||
          row.email ||
          row.EMAIL ||
          row["Email Address"] ||
          row.email_address ||
          row.EMAIL_ADDRESS
      )
      .filter(Boolean)
      .map((email) => email.toString().trim().toLowerCase());

    // Get existing contacts with matching emails
    const existingContacts = await db.Contact.findAll({
      where: {
        email: contactEmailsInSheet,
      },
      attributes: ["id", "name", "email"],
    });

    // Create a map for quick lookup of existing contacts
    const existingEmailMap = new Map();
    existingContacts.forEach((contact) => {
      existingEmailMap.set(contact.email.toLowerCase(), contact);
    });

    // Get all users for assignedTo validation
    const allUsers = await db.User.findAll({
      attributes: ["id", "name", "email"],
    });
    const userMap = new Map();
    allUsers.forEach((user) => {
      userMap.set(user.name.toLowerCase(), user.id);
      userMap.set(user.email.toLowerCase(), user.id);
    });

    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Process each row
    rows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)

      try {
        // Extract contact data with multiple column name variations
        const name = (
          row.Name ||
          row.name ||
          row.NAME ||
          row.ContactName ||
          row["Contact Name"] ||
          row.contact_name
        )
          ?.toString()
          .trim();

        const email = (
          row.Email ||
          row.email ||
          row.EMAIL ||
          row["Email Address"] ||
          row.email_address ||
          row.EMAIL_ADDRESS
        )
          ?.toString()
          .trim()
          .toLowerCase();

        // Validate required fields
        if (!name) {
          errorLog.push({
            row: rowNumber,
            reason: "Contact name is required",
            data: row,
          });
          return;
        }

        if (!email) {
          errorLog.push({
            row: rowNumber,
            reason: "Email is required",
            data: row,
          });
          return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          errorLog.push({
            row: rowNumber,
            reason: "Invalid email format",
            data: row,
          });
          return;
        }

        // Check if contact already exists
        if (existingEmailMap.has(email)) {
          skippedList.push({
            row: rowNumber,
            reason: "Contact with this email already exists",
            data: row,
          });
          return;
        }

        // Extract other optional fields
        const phone =
          (
            row.Phone ||
            row.phone ||
            row.PHONE ||
            row["Phone Number"] ||
            row.phone_number ||
            row.PHONE_NUMBER
          )
            ?.toString()
            .trim() || null;

        const company =
          (
            row.Company ||
            row.company ||
            row.COMPANY ||
            row.Organization ||
            row.organization ||
            row.ORGANIZATION
          )
            ?.toString()
            .trim() || null;

        const message =
          (
            row.Message ||
            row.message ||
            row.MESSAGE ||
            row.Description ||
            row.description ||
            row.DESCRIPTION
          )
            ?.toString()
            .trim() || null;

        const status =
          (row.Status || row.status || row.STATUS)
            ?.toString()
            .trim()
            .toLowerCase() || "new";

        // Validate status
        if (!["new", "in_progress", "resolved", "closed"].includes(status)) {
          errorLog.push({
            row: rowNumber,
            reason: "Status must be one of: new, in_progress, resolved, closed",
            data: row,
          });
          return;
        }

        const priority =
          (row.Priority || row.priority || row.PRIORITY)
            ?.toString()
            .trim()
            .toLowerCase() || "medium";

        // Validate priority
        if (!["low", "medium", "high", "urgent"].includes(priority)) {
          errorLog.push({
            row: rowNumber,
            reason: "Priority must be one of: low, medium, high, urgent",
            data: row,
          });
          return;
        }

        // Handle assignedTo field
        let assignedTo = null;
        const assignedToValue = (
          row.AssignedTo ||
          row.assignedTo ||
          row.assigned_to ||
          row.ASSIGNED_TO ||
          row["Assigned To"] ||
          row.AssignedUser ||
          row.assigned_user ||
          row.ASSIGNED_USER
        )
          ?.toString()
          .trim();

        if (assignedToValue) {
          // Try to find user by name or email
          const userKey = assignedToValue.toLowerCase();
          if (userMap.has(userKey)) {
            assignedTo = userMap.get(userKey);
          } else {
            // If user not found, log as error
            errorLog.push({
              row: rowNumber,
              reason: `Assigned user "${assignedToValue}" not found`,
              data: row,
            });
            return;
          }
        }

        // Handle tags field (JSON array)
        let tags = null;
        const tagsValue = (row.Tags || row.tags || row.TAGS)?.toString().trim();

        if (tagsValue) {
          try {
            // Try to parse as JSON array first
            if (tagsValue.startsWith("[") && tagsValue.endsWith("]")) {
              tags = JSON.parse(tagsValue);
            } else {
              // Split by comma and create array
              tags = tagsValue
                .split(",")
                .map((tag) => tag.trim())
                .filter(Boolean);
            }
          } catch (error) {
            // If parsing fails, treat as comma-separated string
            tags = tagsValue
              .split(",")
              .map((tag) => tag.trim())
              .filter(Boolean);
          }
        }

        const notes =
          (
            row.Notes ||
            row.notes ||
            row.NOTES ||
            row.Comments ||
            row.comments ||
            row.COMMENTS
          )
            ?.toString()
            .trim() || null;

        // Prepare contact data for insertion
        const contactData = {
          name: name,
          email: email,
          phone: phone,
          company: company,
          message: message,
          status: status,
          priority: priority,
          assignedTo: assignedTo,
          tags: tags,
          notes: notes,
        };

        insertList.push(contactData);
        // Add to existing map to prevent duplicates within the same file
        existingEmailMap.set(email, { name: name, email: email });
      } catch (error) {
        errorLog.push({
          row: rowNumber,
          reason: `Processing error: ${error.message}`,
          data: row,
        });
      }
    });

    // Perform bulk insert using transaction
    let insertedContacts = [];
    if (insertList.length > 0) {
      const transaction = await db.sequelize.transaction();
      try {
        insertedContacts = await db.Contact.bulkCreate(insertList, {
          transaction,
          validate: true,
        });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw new Error(`Bulk insert failed: ${error.message}`);
      }
    }

    // Create rejected rows file (skipped + errors) with reasons
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return standardized results for consistent frontend handling
    return {
      totalRows: rows.length,
      insertedCount: insertedContacts.length,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertedContacts,
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    // Create a map of rejected rows by row number for quick lookup
    const rejectedRowsMap = new Map();
    rejectedRows.forEach((rejected) => {
      rejectedRowsMap.set(rejected.row, rejected.reason);
    });

    // Filter original rows to get only rejected ones and add reason column
    const rejectedRowsWithReasons = [];

    originalRows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)
      if (rejectedRowsMap.has(rowNumber)) {
        // Add the reason column to the original row data
        const rowWithReason = {
          ...row,
          "Rejection Reason": rejectedRowsMap.get(rowNumber),
        };
        rejectedRowsWithReasons.push(rowWithReason);
      }
    });

    if (rejectedRowsWithReasons.length === 0) {
      return null;
    }

    // Create new workbook with rejected rows
    const newWorkbook = xlsx.utils.book_new();
    const newWorksheet = xlsx.utils.json_to_sheet(rejectedRowsWithReasons);
    xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, "Rejected Rows");

    // Generate filename for rejected rows file
    const originalFileName = path.basename(
      originalFilePath,
      path.extname(originalFilePath)
    );
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const rejectedFileName = `${originalFileName}_rejected_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Write the rejected rows file
    xlsx.writeFile(newWorkbook, rejectedFilePath);

    // Return the download URL (relative to uploads directory)
    const uploadsDir = path.join(__basedir, "uploads");
    const relativePath = path.relative(uploadsDir, rejectedFilePath);
    return `/uploads/${relativePath.replace(/\\/g, "/")}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// helper function
async function getSingleRecord(id) {
  const record = await db.Contact.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
