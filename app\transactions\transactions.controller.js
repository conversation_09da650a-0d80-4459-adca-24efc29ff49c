const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const transactionService = require("./transaction.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/transactions";
    cb(null, __basedir + "uploads/transactions");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", transactionSchema, create);
router.post("/insert-transactions", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", transactionSchema, update);
router.delete("/:id", _delete);

// Dashboard Analytics Routes
router.get("/dashboard/summary", getDashboardSummary);
router.get("/dashboard/revenue-over-time", getRevenueOverTime);
router.get("/dashboard/year-comparison", getYearOverYearComparison);
router.get("/dashboard/revenue-by-category", getRevenueByCategory);
router.get("/dashboard/top-clients", getTopClients);
router.get("/dashboard/filter-options", getFilterOptions);

// Consolidated Dashboard API
router.post("/dashboard/all-data", getAllDashboardData);

// Reports API
router.post("/reports/transactions", generateTransactionReport);
router.post("/reports/stage-customers", generateStageCustomerReport);

// Sales Data API
router.get("/sales-data/byYear/:year", getSalesDataByYear);
router.get("/sales-data/daily-revenue", getDailyRevenueLast7Days);
router.get("/top-customers", getTopCustomersByRevenue);
router.get("/top-channels", getTopChannelsByRevenue);
router.get("/monthly-channel-revenue", getMonthlyChannelRevenue);
router.get("/all-channels-revenue", getAllChannelsRevenue);

// Consolidated Analytics API
router.get("/analytics/all-data", getAllAnalyticsData);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  transactionService
    .create(req.body)
    .then((transaction) => {
      logRequest(
        req,
        `Created a new Transaction with ID: "${transaction?.id}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  transactionService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Transactions", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  transactionService
    .getById(req.params.id)
    .then((transaction) => {
      logRequest(
        req,
        `Fetched Transaction with ID: "${transaction?.id}"`,
        "READ"
      );
      res.json(transaction);
    })
    .catch(next);
}

function transactionSchema(req, res, next) {
  const schema = Joi.object({
    customerId: Joi.number().integer().required(),
    customerEmployeeId: Joi.number().integer().allow(null),
    mediumId: Joi.number().integer().required(),
    channelId: Joi.number().integer().required(),
    propertyId: Joi.number().integer().required(),
    variantId: Joi.number().integer().optional().allow(null),
    cityId: Joi.number().integer().optional().allow(null),

    paymentStatus: Joi.string()
      .valid("pending", "partial", "paid", "cancelled")
      .optional(),
    paymentDate: Joi.date().optional().allow(null),
    paymentMethod: Joi.string().optional().allow(null, ""),
    paymentReference: Joi.string().optional().allow(null, ""),
    invoiceNumber: Joi.string().optional().allow(null, ""),
    invoiceDate: Joi.date().optional().allow(null),
    poNumber: Joi.string().optional().allow(null, ""),
    description: Joi.string().optional().allow(null, ""),
    quantity: Joi.number().integer().min(1).optional().allow(null),
    month: Joi.string()
      .valid(
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December"
      )
      .optional()
      .allow(null, ""),
    year: Joi.number().integer().min(1900).max(2100).optional().allow(null),
    revenue: Joi.number().precision(2).optional().allow(null),
    notes: Joi.string().optional().allow(null, ""),
    stage: Joi.string().optional().allow(null, ""),
    status: Joi.string().valid("active", "inactive", "cancelled").optional(),
    attachments: Joi.array().items(Joi.string()).optional().allow(null),
    createdBy: Joi.number().integer().optional().allow(null),
    updatedBy: Joi.number().integer().optional().allow(null),
    deletedBy: Joi.number().integer().optional().allow(null),
    deletedAt: Joi.date().optional().allow(null),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  transactionService
    .update(req.params.id, req.body)
    .then((transaction) => {
      logRequest(
        req,
        `Updated Transaction with ID: "${transaction?.id}"`,
        "UPDATE"
      );
      res.json({ status: true, message: "Record updated successfully" });
    })
    .catch(next);
}

function _delete(req, res, next) {
  const userId = req?.headers?.userid;

  transactionService
    .delete(req.params.id, userId)
    .then(() => {
      logRequest(
        req,
        `Deleted Transaction with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

// Dashboard Analytics Controller Functions

function getDashboardSummary(req, res, next) {
  transactionService
    .getDashboardSummary(req.query)
    .then((data) => {
      logRequest(req, "Fetched Dashboard Summary", "READ");
      res.json(data);
    })
    .catch(next);
}

function getRevenueOverTime(req, res, next) {
  transactionService
    .getRevenueOverTime(req.query)
    .then((data) => {
      logRequest(req, "Fetched Revenue Over Time", "READ");
      res.json(data);
    })
    .catch(next);
}

function getYearOverYearComparison(req, res, next) {
  transactionService
    .getYearOverYearComparison(req.query)
    .then((data) => {
      logRequest(req, "Fetched Year-over-Year Comparison", "READ");
      res.json(data);
    })
    .catch(next);
}

function getRevenueByCategory(req, res, next) {
  transactionService
    .getRevenueByCategory(req.query)
    .then((data) => {
      logRequest(req, "Fetched Revenue by Category", "READ");
      res.json(data);
    })
    .catch(next);
}

function getTopClients(req, res, next) {
  transactionService
    .getTopClients(req.query)
    .then((data) => {
      logRequest(req, "Fetched Top Clients", "READ");
      res.json(data);
    })
    .catch((error) => {
      console.error("Error in getTopClients:", error);
      next(error);
    });
}

function getFilterOptions(req, res, next) {
  transactionService
    .getFilterOptions()
    .then((data) => {
      logRequest(req, "Fetched Filter Options", "READ");
      res.json(data);
    })
    .catch(next);
}

function getAllDashboardData(req, res, next) {
  // Use request body for POST request instead of query parameters
  const filters = req.body || {};

  transactionService
    .getAllDashboardData(filters)
    .then((data) => {
      logRequest(req, "Fetched All Dashboard Data", "READ");
      res.json(data);
    })
    .catch((error) => {
      console.error("Error in getAllDashboardData:", error);
      next(error);
    });
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  transactionService
    .bulkImport(filePath, userId)
    .then((result) => {
      logRequest(
        req,
        `Bulk imported transactions: ${result.insertedCount} inserted, ${result.skippedCount} skipped, ${result.errorCount} errors`,
        "BULK_IMPORT"
      );

      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl, // Download link for rejected rows
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}

// Reports Controller Function
function generateTransactionReport(req, res, next) {
  // Use request body for POST request to get filter parameters
  const filters = req.body || {};

  transactionService
    .generateTransactionReport(filters)
    .then((result) => {
      logRequest(
        req,
        `Generated transaction report with ${result.totalRecords} records`,
        "REPORT_GENERATION"
      );
      res.json({
        status: true,
        message: result.message,
        data: {
          downloadUrl: result.downloadUrl,
          totalRecords: result.totalRecords,
          totalRevenue: result.totalRevenue,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Transaction report generation failed: ${error.message}`,
        "REPORT_GENERATION_ERROR"
      );
      console.error("Error in generateTransactionReport:", error);
      next(error);
    });
}

// Stage Customer Reports Controller Function
function generateStageCustomerReport(req, res, next) {
  // Use request body for POST request to get stage parameters
  const filters = req.body || {};

  transactionService
    .generateStageCustomerReport(filters)
    .then((result) => {
      logRequest(
        req,
        `Generated stage customer report with ${
          result.totalRecords
        } records for stages: ${result.stages.join(", ")}`,
        "STAGE_REPORT_GENERATION"
      );
      res.json({
        status: true,
        message: result.message,
        data: {
          downloadUrl: result.downloadUrl,
          totalRecords: result.totalRecords,
          stages: result.stages,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Stage customer report generation failed: ${error.message}`,
        "STAGE_REPORT_GENERATION_ERROR"
      );
      console.error("Error in generateStageCustomerReport:", error);
      next(error);
    });
}

// Sales Data Controller Function
function getSalesDataByYear(req, res, next) {
  const year = parseInt(req.params.year);
  
  // Validate year parameter
  if (!year || year < 1900 || year > 2100) {
    return res.status(400).json({
      status: false,
      message: "Invalid year parameter. Year must be between 1900 and 2100."
    });
  }

  transactionService
    .getSalesDataByYear(year)
    .then((data) => {
      logRequest(req, `Fetched Sales Data for year ${year}`, "READ");
      res.json({
        status: true,
        message: `Sales data retrieved successfully for year ${year}`,
        data: data
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Sales data fetch failed for year ${year}: ${error.message}`,
        "SALES_DATA_ERROR"
      );
      console.error("Error in getSalesDataByYear:", error);
      next(error);
    });
}

// Daily Revenue Controller Function
function getDailyRevenueLast7Days(req, res, next) {
  transactionService
    .getDailyRevenueLast7Days()
    .then((data) => {
      logRequest(req, "Fetched Daily Revenue for Last 7 Days", "READ");
      res.json({
        status: true,
        message: "Daily revenue data retrieved successfully for the last 7 days",
        data: data
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Daily revenue fetch failed: ${error.message}`,
        "DAILY_REVENUE_ERROR"
      );
      console.error("Error in getDailyRevenueLast7Days:", error);
      next(error);
    });
}

// Top Customers Controller Function
function getTopCustomersByRevenue(req, res, next) {
  const limit = parseInt(req.query.limit) || 10;
  
  // Validate limit parameter
  if (limit < 1 || limit > 100) {
    return res.status(400).json({
      status: false,
      message: "Invalid limit parameter. Limit must be between 1 and 100."
    });
  }

  transactionService
    .getTopCustomersByRevenue(limit)
    .then((data) => {
      logRequest(req, `Fetched Top ${limit} Customers by Revenue`, "READ");
      res.json({
        status: true,
        message: `Top ${limit} customers by revenue retrieved successfully`,
        data: data
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Top customers fetch failed: ${error.message}`,
        "TOP_CUSTOMERS_ERROR"
      );
      console.error("Error in getTopCustomersByRevenue:", error);
      next(error);
    });
}

// Top Channels Controller Function
function getTopChannelsByRevenue(req, res, next) {
  const limit = parseInt(req.query.limit) || 10;
  
  // Validate limit parameter
  if (limit < 1 || limit > 100) {
    return res.status(400).json({
      status: false,
      message: "Invalid limit parameter. Limit must be between 1 and 100."
    });
  }

  transactionService
    .getTopChannelsByRevenue(limit)
    .then((data) => {
      logRequest(req, `Fetched Top ${limit} Channels by Revenue`, "READ");
      res.json({
        status: true,
        message: `Top ${limit} channels by revenue retrieved successfully`,
        data: data
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Top channels fetch failed: ${error.message}`,
        "TOP_CHANNELS_ERROR"
      );
      console.error("Error in getTopChannelsByRevenue:", error);
      next(error);
    });
}

// Monthly Channel Revenue Controller Function
function getMonthlyChannelRevenue(req, res, next) {
  transactionService
    .getMonthlyChannelRevenue()
    .then((data) => {
      logRequest(req, "Fetched Monthly Channel Revenue Data", "READ");
      res.json({
        status: true,
        message: "Monthly channel revenue data retrieved successfully",
        data: data
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Monthly channel revenue fetch failed: ${error.message}`,
        "MONTHLY_CHANNEL_REVENUE_ERROR"
      );
      console.error("Error in getMonthlyChannelRevenue:", error);
      next(error);
    });
}

// All Channels Revenue Controller Function
function getAllChannelsRevenue(req, res, next) {
  transactionService
    .getAllChannelsRevenue()
    .then((data) => {
      logRequest(req, "Fetched All Channels Revenue Data", "READ");
      res.json({
        status: true,
        message: "All channels revenue data retrieved successfully",
        data: data
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `All channels revenue fetch failed: ${error.message}`,
        "ALL_CHANNELS_REVENUE_ERROR"
      );
      console.error("Error in getAllChannelsRevenue:", error);
      next(error);
    });
}

// Consolidated Analytics Controller Function
function getAllAnalyticsData(req, res, next) {
  const params = {
    year: parseInt(req.query.year) || new Date().getFullYear(),
    month: req.query.month || 'all',
    limit: parseInt(req.query.limit) || 10
  };

  transactionService
    .getAllAnalyticsData(params)
    .then((data) => {
      logRequest(req, "Fetched All Analytics Data", "READ");
      res.json({
        status: true,
        message: "All analytics data retrieved successfully",
        data: data
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Analytics data fetch failed: ${error.message}`,
        "ANALYTICS_DATA_ERROR"
      );
      console.error("Error in getAllAnalyticsData:", error);
      next(error);
    });
}
