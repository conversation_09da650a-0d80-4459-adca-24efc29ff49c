const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const propertyService = require("./property.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/properties";
    cb(null, __basedir + "uploads/properties");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", propertySchema, create);
router.post("/insert-properties", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", propertySchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  propertyService
    .create(req.body)
    .then((property) => {
      logRequest(
        req,
        `Created a new Property with name: "${property?.name}" and status: "${property?.status}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  propertyService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Properties", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  propertyService
    .getById(req.params.id)
    .then((property) => {
      logRequest(
        req,
        `Fetched Property with name: "${property?.name}"`,
        "READ"
      );
      res.json(property);
    })
    .catch(next);
}

function propertySchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    channelId: Joi.number().integer().required(),
    mediumId: Joi.number().integer().required(),
    slug: Joi.string().optional(), // Auto-generated, so optional
    code: Joi.string().optional(), // Auto-generated, so optional
    description: Joi.string().optional(),
    status: Joi.string().valid("active", "inactive").optional(),
    createdBy: Joi.number().integer().optional(),
    updatedBy: Joi.number().integer().optional(),
    deletedBy: Joi.number().integer().optional(),
    deletedAt: Joi.date().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  propertyService
    .update(req.params.id, req.body)
    .then((property) => {
      logRequest(
        req,
        `Updated Property with name: "${property?.name}" and status: "${property?.status}"`,
        "UPDATE"
      );
      res.json(property);
    })
    .catch(next);
}

function _delete(req, res, next) {
  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;

  propertyService
    .delete(req.params.id, userId)
    .then(() => {
      logRequest(req, `Deleted Property with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  propertyService
    .bulkImport(filePath, userId)
    .then((result) => {
      logRequest(
        req,
        `Bulk imported properties: ${result.insertedCount} inserted, ${result.skippedCount} skipped, ${result.errorCount} errors`,
        "BULK_IMPORT"
      );

      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}
