const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  const whereClause = {
    deletedAt: null, // Exclude soft-deleted records
  };

  // Add additional filters if provided
  if (params?.status) {
    whereClause.status = params.status;
  }
  if (params?.channelId) {
    whereClause.channelId = params.channelId;
  }

  return await db.Property.findAll({
    where: whereClause,
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Generate slug from name
  params.slug = utils.generateSlug(params.name);
  // Generate code from name
  params.code = utils.generateSlug(params.name);
  // Set default status if not provided
  if (!params.status) params.status = "active";
  // Set audit fields
  params.createdBy = params.userId || null;

  const record = await db.Property.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Update slug and code if name is changed
  if (params.name && params.name !== record.name) {
    params.slug = utils.generateSlug(params.name);
    params.code = utils.generateSlug(params.name);
  }

  // Set audit fields
  params.updatedBy = params.userId || null;

  // copy params to Property and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id, userId) {
  const record = await getSingleRecord(id);

  // Soft delete
  record.deletedBy = userId || null;
  record.deletedAt = new Date();
  await record.save();

  // Or hard delete if preferred
  // await record.destroy();
}

async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Collect medium names from Excel and generate slugs for lookup
    const mediumNamesInSheet = rows
      .map(
        (row) =>
          row.Medium || row.medium || row.MediumName || row["Medium Name"]
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const mediumSlugsInSheet = mediumNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Preload existing media by slug to get mediumId
    const existingMedia = await db.Medium.findAll({
      where: {
        slug: mediumSlugsInSheet,
        deletedAt: null,
      },
      attributes: ["id", "slug", "name"],
    });

    const mediumSlugMap = new Map();
    existingMedia.forEach((medium) => {
      mediumSlugMap.set(medium.slug, medium);
    });

    // Collect channel names from Excel and generate slugs for lookup
    const channelNamesInSheet = rows
      .map(
        (row) =>
          row.Channel || row.channel || row.ChannelName || row["Channel Name"]
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const channelSlugsInSheet = channelNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Preload existing channels by slug to get channelId
    const existingChannels = await db.Channel.findAll({
      where: {
        slug: channelSlugsInSheet,
        deletedAt: null,
      },
      attributes: ["id", "slug", "name"],
    });

    const channelSlugMap = new Map();
    existingChannels.forEach((channel) => {
      channelSlugMap.set(channel.slug, channel);
    });

    // Collect property names from Excel and generate slugs for duplicate checking
    const propertyNamesInSheet = rows
      .map(
        (row) =>
          row.Name || row.name || row.PropertyName || row["Property Name"]
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const propertySlugsInSheet = propertyNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Preload existing properties by slug to check for duplicates
    const existingProperties = await db.Property.findAll({
      where: {
        slug: propertySlugsInSheet,
        deletedAt: null,
      },
      attributes: ["id", "slug", "name"],
    });

    const existingPropertySlugMap = new Map();
    existingProperties.forEach((property) => {
      existingPropertySlugMap.set(property.slug, property);
    });

    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Process each row
    for (let index = 0; index < rows.length; index++) {
      const row = rows[index];
      const name = (
        row.Name ||
        row.name ||
        row.PropertyName ||
        row["Property Name"]
      )
        ?.toString()
        .trim();
      const mediumName = (
        row.Medium ||
        row.medium ||
        row.MediumName ||
        row["Medium Name"]
      )
        ?.toString()
        .trim();
      const channelName = (
        row.Channel ||
        row.channel ||
        row.ChannelName ||
        row["Channel Name"]
      )
        ?.toString()
        .trim();
      const description = (row.Description || row.description)
        ?.toString()
        .trim();

      if (!name) {
        errorLog.push({
          row: index + 2,
          reason: "Missing Property Name",
          data: row,
        });
        continue;
      }

      if (!mediumName) {
        errorLog.push({
          row: index + 2,
          reason: "Missing Medium Name",
          data: row,
        });
        continue;
      }

      if (!channelName) {
        errorLog.push({
          row: index + 2,
          reason: "Missing Channel Name",
          data: row,
        });
        continue;
      }

      const propertySlug = utils.generateSlug(name);
      const mediumSlug = utils.generateSlug(mediumName);
      const channelSlug = utils.generateSlug(channelName);

      // Find or create medium
      let medium = mediumSlugMap.get(mediumSlug);
      if (!medium) {
        // Create new medium
        try {
          const newMedium = await db.Medium.create({
            name: mediumName,
            slug: mediumSlug,
            code: mediumSlug,
            status: "active",
            createdBy: userId || null,
          });
          medium = newMedium;
          mediumSlugMap.set(mediumSlug, newMedium);
        } catch (error) {
          errorLog.push({
            row: index + 2,
            reason: `Failed to create medium: ${error.message}`,
            data: row,
          });
          continue;
        }
      }

      // Find or create channel
      let channel = channelSlugMap.get(channelSlug);
      if (!channel) {
        // Create new channel - link it to the medium
        try {
          const newChannel = await db.Channel.create({
            mediumId: medium.id,
            name: channelName,
            slug: channelSlug,
            code: channelSlug,
            status: "active",
            createdBy: userId || null,
          });
          channel = newChannel;
          channelSlugMap.set(channelSlug, newChannel);
        } catch (error) {
          errorLog.push({
            row: index + 2,
            reason: `Failed to create channel: ${error.message}`,
            data: row,
          });
          continue;
        }
      }

      // Check for duplicate property by slug
      if (existingPropertySlugMap.has(propertySlug)) {
        skippedList.push({
          row: index + 2,
          name: name,
          reason: `Duplicate property found: ${
            existingPropertySlugMap.get(propertySlug).name
          }`,
          existingProperty: existingPropertySlugMap.get(propertySlug),
        });
        continue;
      }

      // Get foreign key IDs from the created/found entities
      const mediumId = medium.id;
      const channelId = channel.id;

      // Prepare property data for insertion
      const propertyData = {
        name: name,
        slug: propertySlug,
        code: propertySlug,
        mediumId: mediumId,
        channelId: channelId,
        description: description || null,
        status: "active",
        createdBy: userId || null,
      };

      insertList.push(propertyData);
      // Add to existing map to prevent duplicates within the same file
      existingPropertySlugMap.set(propertySlug, {
        name: name,
        slug: propertySlug,
      });
    }

    // Perform bulk insert using transaction
    let insertedProperties = [];
    if (insertList.length > 0) {
      const transaction = await db.sequelize.transaction();
      try {
        insertedProperties = await db.Property.bulkCreate(insertList, {
          transaction,
          validate: true,
        });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw new Error(`Bulk insert failed: ${error.message}`);
      }
    }

    // Create rejected rows file (skipped + errors) with reasons
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return simplified results for consistent frontend handling
    return {
      totalRows: rows.length,
      insertedCount: insertedProperties.length,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertedProperties,
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    // Create a map of rejected rows by row number for quick lookup
    const rejectedRowsMap = new Map();
    rejectedRows.forEach((rejected) => {
      rejectedRowsMap.set(rejected.row, rejected.reason);
    });

    // Filter original rows to get only rejected ones and add reason column
    const rejectedRowsWithReasons = [];

    originalRows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)
      if (rejectedRowsMap.has(rowNumber)) {
        // Add the reason column to the original row data
        const rowWithReason = {
          ...row,
          "Rejection Reason": rejectedRowsMap.get(rowNumber),
        };
        rejectedRowsWithReasons.push(rowWithReason);
      }
    });

    if (rejectedRowsWithReasons.length === 0) {
      return null;
    }

    // Generate unique filename for rejected rows
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const originalFileName = path.basename(
      originalFilePath,
      path.extname(originalFilePath)
    );
    const rejectedFileName = `${originalFileName}_rejected_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Create new workbook with rejected rows
    const workbook = xlsx.utils.book_new();
    const worksheet = xlsx.utils.json_to_sheet(rejectedRowsWithReasons);
    xlsx.utils.book_append_sheet(workbook, worksheet, "Rejected Rows");

    // Write the rejected rows file
    xlsx.writeFile(workbook, rejectedFilePath);

    // Return the download URL (relative path from uploads directory with /api prefix)
    const relativePath = path.relative(__basedir + "uploads", rejectedFilePath);
    return `/uploads/${relativePath.replace(/\\/g, "/")}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Property.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
