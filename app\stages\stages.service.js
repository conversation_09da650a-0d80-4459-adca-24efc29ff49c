const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  _delete,
};

async function getAll(query = {}) {
  const { page = 1, limit = 10, search = "", status } = query;
  const offset = (page - 1) * limit;

  let whereClause = { deletedAt: null };

  if (search) {
    whereClause.name = { [db.Sequelize.Op.like]: `%${search}%` };
  }

  if (status) {
    whereClause.status = status;
  }

  const { count, rows } = await db.Stage.findAndCountAll({
    where: whereClause,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [["name", "ASC"]],
  });

  return {
    data: rows,
    meta: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      totalPages: Math.ceil(count / limit),
    },
  };
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Generate slug from name
  params.slug = utils.generateSlug(params.name);
  // Set default status if not provided
  if (!params.status) params.status = "active";
  // Set audit fields
  params.createdBy = params.userId || null;

  const record = await db.Stage.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Update slug if name is changed
  if (params.name && params.name !== record.name) {
    params.slug = utils.generateSlug(params.name);
  }

  // Set audit fields
  params.updatedBy = params.userId || null;

  // copy params to Stage and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id, userId) {
  const record = await getSingleRecord(id);

  // Soft delete
  record.deletedBy = userId || null;
  record.deletedAt = new Date();
  await record.save();
}

// Helper functions
async function getSingleRecord(id) {
  const record = await db.Stage.findOne({
    where: {
      id,
      deletedAt: null,
    },
  });

  if (!record) throw "Record not found";

  return record;
}
