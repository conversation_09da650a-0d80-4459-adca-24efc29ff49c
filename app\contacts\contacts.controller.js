const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const contactService = require("./contact.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/contacts";
    cb(null, __basedir + "uploads/contacts");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", contactSchema, create);
router.post("/insert-contacts", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", contactSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  contactService
    .create(req.body)
    .then((contact) => {
      logRequest(
        req,
        `Created a new Contact: "${contact?.name}" with status: "${contact?.status}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  contactService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Contacts", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  contactService
    .getById(req.params.id)
    .then((contact) => {
      logRequest(
        req,
        `Fetched Contact: "${contact?.name}" with status: "${contact?.status}"`,
        "READ"
      );
      res.json(contact);
    })
    .catch(next);
}

function contactSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().optional(),
    company: Joi.string().optional(),
    message: Joi.string().optional(),
    status: Joi.string()
      .valid("new", "in_progress", "resolved", "closed")
      .optional(),
    priority: Joi.string().valid("low", "medium", "high", "urgent").optional(),
    assignedTo: Joi.number().integer().optional(),
    tags: Joi.array().optional(), // JSON field for tags
    notes: Joi.string().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  contactService
    .update(req.params.id, req.body)
    .then((contact) => {
      logRequest(
        req,
        `Updated Contact: "${contact?.name}" with status: "${contact?.status}" and priority: "${contact?.priority}"`,
        "UPDATE"
      );
      res.json(contact);
    })
    .catch(next);
}

function _delete(req, res, next) {
  contactService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted Contact with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  contactService
    .bulkImport(filePath, userId)
    .then((result) => {
      logRequest(
        req,
        `Bulk imported contacts: ${result.insertedCount} inserted, ${result.skippedCount} skipped, ${result.errorCount} errors`,
        "BULK_IMPORT"
      );

      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}
