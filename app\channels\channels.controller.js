const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const channelService = require("./channel.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/channels";
    cb(null, __basedir + "uploads/channels");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", channelSchema, create);
router.post("/insert-channels", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", channelSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  channelService
    .create(req.body)
    .then((channel) => {
      logRequest(
        req,
        `Created a new Channel with name: "${channel?.name}" and status: "${channel?.status}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  channelService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Channels", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  channelService
    .getById(req.params.id)
    .then((channel) => {
      logRequest(req, `Fetched Channel with name: "${channel?.name}"`, "READ");
      res.json(channel);
    })
    .catch(next);
}

function channelSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    mediumId: Joi.number().integer().required(),
    slug: Joi.string().optional(), // Auto-generated, so optional
    code: Joi.string().optional(), // Auto-generated, so optional
    description: Joi.string().optional(),
    status: Joi.string().valid("active", "inactive").optional(),
    createdBy: Joi.number().integer().optional(),
    updatedBy: Joi.number().integer().optional(),
    deletedBy: Joi.number().integer().optional(),
    deletedAt: Joi.date().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  channelService
    .update(req.params.id, req.body)
    .then((channel) => {
      logRequest(
        req,
        `Updated Channel with name: "${channel?.name}" and status: "${channel?.status}"`,
        "UPDATE"
      );
      res.json(channel);
    })
    .catch(next);
}

function _delete(req, res, next) {
  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;

  channelService
    .delete(req.params.id, userId)
    .then(() => {
      logRequest(req, `Deleted Channel with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  channelService
    .bulkImport(filePath, userId)
    .then((result) => {
      logRequest(
        req,
        `Bulk imported channels: ${result.insertedCount} inserted, ${result.skippedCount} skipped, ${result.errorCount} errors`,
        "BULK_IMPORT"
      );

      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}
