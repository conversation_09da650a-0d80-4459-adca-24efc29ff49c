const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const mediumService = require("./medium.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/mediums";
    cb(null, __basedir + "uploads/mediums");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", mediumSchema, create);
router.post("/insert-mediums", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", mediumSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit
  req.body.userId = req.headers.userid;

  mediumService
    .create(req.body)
    .then((medium) => {
      logRequest(
        req,
        `Created a new Medium with name: "${medium?.name}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  mediumService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Mediums", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  mediumService
    .getById(req.params.id)
    .then((medium) => {
      logRequest(req, `Fetched Medium with name: "${medium?.name}"`, "READ");
      res.json(medium);
    })
    .catch(next);
}

function mediumSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().allow(null, ""),
    status: Joi.string().valid("active", "inactive").default("active"),
    // Don't validate these fields as they're generated or set internally
    slug: Joi.string().optional(),
    code: Joi.string().optional(),
    createdBy: Joi.number().optional(),
    updatedBy: Joi.number().optional(),
    deletedBy: Joi.number().optional(),
    deletedAt: Joi.date().optional(),
    // For passing user ID from controller to service
    userId: Joi.number().optional(),
    // Any other fields needed for your form
    pageName: Joi.string().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  // Add userId from headers for audit
  req.body.userId = req.headers.userid;

  mediumService
    .update(req.params.id, req.body)
    .then((medium) => {
      logRequest(
        req,
        `Updated Medium with name: "${medium?.name}" and status: "${medium?.status}"`,
        "UPDATE"
      );
      res.json(medium);
    })
    .catch(next);
}

function _delete(req, res, next) {
  const userId = req.headers.userid;

  mediumService
    .delete(req.params.id, userId)
    .then(() => {
      logRequest(req, `Deleted Medium with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  mediumService
    .bulkImport(filePath, userId)
    .then((result) => {
      logRequest(
        req,
        `Bulk imported mediums: ${result.insertedCount} inserted, ${result.skippedCount} skipped, ${result.errorCount} errors`,
        "BULK_IMPORT"
      );

      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}
