const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const customerService = require("./customer.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/customers";
    cb(null, __basedir + "uploads/customers");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", customerSchema, create);
router.post("/insert-customers", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", customerSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  customerService
    .create(req.body)
    .then((customer) => {
      logRequest(
        req,
        `Created a new Customer with name: "${customer?.name}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  customerService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Customers", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  customerService
    .getById(req.params.id)
    .then((customer) => {
      logRequest(
        req,
        `Fetched Customer with name: "${customer?.name}"`,
        "READ"
      );
      res.json(customer);
    })
    .catch(next);
}

function customerSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    slug: Joi.string().optional(),
    code: Joi.string().optional(),
    email: Joi.string().email().allow(null, ""),
    phone: Joi.string().allow(null, ""),
    address: Joi.string().allow(null, ""),
    cityId: Joi.number().integer().allow(null),
    state: Joi.string().allow(null, ""),
    country: Joi.string().default("India"),
    pincode: Joi.string().allow(null, ""),
    website: Joi.string().allow(null, ""),
    companyCategoryId: Joi.number().integer().allow(null),
    industry: Joi.string().allow(null, ""),
    gstNumber: Joi.string().allow(null, ""),
    panNumber: Joi.string().allow(null, ""),
    logo: Joi.string().allow(null, ""),
    status: Joi.string().default("active"),
    notes: Joi.string().allow(null, ""),
    // Don't validate these fields as they're generated or set internally
    // createdBy, updatedBy, deletedBy, deletedAt
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  customerService
    .update(req.params.id, req.body)
    .then((customer) => {
      logRequest(
        req,
        `Updated Customer with name: "${customer?.name}" and status: "${customer?.status}"`,
        "UPDATE"
      );
      res.json({ status: true, message: "Record updated successfully" });
    })
    .catch(next);
}

function _delete(req, res, next) {
  const userId = req?.headers?.userid;

  customerService
    .delete(req.params.id, userId)
    .then(() => {
      logRequest(req, `Deleted Customer with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  customerService
    .bulkImport(filePath, userId)
    .then((result) => {
      logRequest(
        req,
        `Bulk imported customers: ${result.insertedCount} inserted, ${result.skippedCount} skipped, ${result.errorCount} errors`,
        "BULK_IMPORT"
      );

      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}
