const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const customerEmployeeService = require("./customer-employee.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/customer-employees";
    cb(null, __basedir + "uploads/customer-employees");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

function logRequest(req, message, action) {
  logAction(req?.headers?.userid, action, "CustomerEmployee", message);
}

// Routes
router.get("/", getAll);
router.post("/", customerEmployeeSchema, create);
router.post("/insert-customer-employees", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", customerEmployeeSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  customerEmployeeService
    .create(req.body)
    .then((employee) => {
      logRequest(
        req,
        `Created a new Customer Employee with name: "${employee?.name}" for customer ID: "${employee?.customerId}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  customerEmployeeService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Customer Employees", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  customerEmployeeService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched Customer Employee with ID: ${req.params.id}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  customerEmployeeService
    .update(req.params.id, req.body)
    .then((employee) => {
      logRequest(
        req,
        `Updated Customer Employee with ID: ${req.params.id}`,
        "UPDATE"
      );
      res.json({ status: true, message: "Record updated successfully" });
    })
    .catch(next);
}

function _delete(req, res, next) {
  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;

  customerEmployeeService
    .delete(req.params.id, userId)
    .then(() => {
      logRequest(
        req,
        `Deleted Customer Employee with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function customerEmployeeSchema(req, res, next) {
  const schema = Joi.object({
    customerId: Joi.number().integer().allow(null),
    name: Joi.string().required(),
    email: Joi.string().email().allow(null, ""),
    phone: Joi.string().allow(null, ""),
    designation: Joi.string().allow(null, ""),
    department: Joi.string().allow(null, ""),
    isPrimary: Joi.boolean().default(false),
    status: Joi.string().valid("active", "inactive").default("active"),
    notes: Joi.string().allow(null, ""),
    // Don't validate these fields as they're generated or set internally
    createdBy: Joi.number().integer().optional(),
    updatedBy: Joi.number().integer().optional(),
    deletedBy: Joi.number().integer().optional(),
    deletedAt: Joi.date().optional(),
    // For passing user ID from controller to service
    userId: Joi.number().optional(),
  });
  validateRequest(req, next, schema);
}

function bulkImport(req, res, next) {
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  customerEmployeeService
    .bulkImport(filePath, userId)
    .then((result) => {
      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}
