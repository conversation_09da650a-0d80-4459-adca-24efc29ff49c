const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const companyService = require("./company.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/companies";
    cb(null, __basedir + "uploads/companies");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

function logRequest(req, message, action) {
  const userId = req?.headers?.userid;
  if (userId) {
    logAction(userId, action, message);
  }
}

// Routes
router.get("/", getAll);
router.post("/", companySchema, create);
router.post("/insert-companies", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", companySchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  companyService
    .create(req.body)
    .then((company) => {
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  companyService
    .getAll(req.query)
    .then((records) => {
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  companyService
    .getById(req.params.id)
    .then((company) => {
      res.json(company);
    })
    .catch(next);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  companyService
    .update(req.params.id, req.body)
    .then((company) => {
      res.json({ status: true, message: "Record updated successfully" });
    })
    .catch(next);
}

function _delete(req, res, next) {
  const userId = req?.headers?.userid;

  companyService
    .delete(req.params.id, userId)
    .then(() => {
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  companyService
    .bulkImport(filePath, userId)
    .then((result) => {
      logRequest(
        req,
        `Bulk imported companies: ${result.insertedCount} inserted, ${result.skippedCount} skipped, ${result.errorCount} errors`,
        "BULK_IMPORT"
      );

      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}

function companySchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().allow(null, ""),
    website: Joi.string().allow(null, ""),
    address: Joi.string().allow(null, ""),
    city: Joi.string().allow(null, ""),
    state: Joi.string().allow(null, ""),
    country: Joi.string().allow(null, ""),
    type: Joi.string().valid("client", "vendor", "partner").required(),
    industry: Joi.string().allow(null, ""),
    taxId: Joi.string().allow(null, ""),
    registrationNumber: Joi.string().allow(null, ""),
    status: Joi.string()
      .valid("active", "inactive", "pending")
      .default("active"),
    notes: Joi.string().allow(null, ""),
    // Don't validate these fields as they're generated or set internally
    slug: Joi.string().optional(),
    code: Joi.string().optional(),
    createdBy: Joi.number().optional(),
    updatedBy: Joi.number().optional(),
    deletedBy: Joi.number().optional(),
    deletedAt: Joi.date().optional(),
    // For passing user ID from controller to service
    userId: Joi.number().optional(),
  });
  validateRequest(req, next, schema);
}
