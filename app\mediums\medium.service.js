const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  return await db.Medium.findAll({
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Generate slug from name
  params.slug = utils.generateSlug(params.name);
  // Generate code from name
  params.code = utils.generateSlug(params.name);
  // Set default status if not provided
  if (!params.status) params.status = "active";
  // Set audit fields
  params.createdBy = params.userId || null;

  const record = await db.Medium.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Update slug if name is changed
  if (params.name && params.name !== record.name) {
    params.slug = utils.generateSlug(params.name);
    params.code = utils.generateSlug(params.name);
  }

  // Set audit fields
  params.updatedBy = params.userId || null;

  // copy params to Medium and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id, userId) {
  const record = await getSingleRecord(id);

  // Soft delete
  record.deletedBy = userId || null;
  record.deletedAt = new Date();
  await record.save();

  // Or hard delete if preferred
  await record.destroy();
}

async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Collect medium names from Excel and generate slugs
    const mediumNamesInSheet = rows
      .map(
        (row) =>
          row.Name ||
          row.name ||
          row.NAME ||
          row.MediumName ||
          row["Medium Name"] ||
          row.medium_name ||
          row.MEDIUM_NAME
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const slugsInSheet = mediumNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Get existing mediums with matching slugs
    const existingMediums = await db.Medium.findAll({
      where: {
        slug: slugsInSheet,
        deletedAt: null,
      },
      attributes: ["id", "name", "slug"],
    });

    // Create a map for quick lookup of existing mediums
    const existingSlugMap = new Map();
    existingMediums.forEach((medium) => {
      existingSlugMap.set(medium.slug, medium);
    });

    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Process each row
    rows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)

      try {
        // Extract medium data with multiple column name variations
        const name = (
          row.Name ||
          row.name ||
          row.NAME ||
          row.MediumName ||
          row["Medium Name"] ||
          row.medium_name ||
          row.MEDIUM_NAME
        )
          ?.toString()
          .trim();

        // Validate required fields
        if (!name) {
          errorLog.push({
            row: rowNumber,
            reason: "Medium name is required",
            data: row,
          });
          return;
        }

        const slug = utils.generateSlug(name);

        // Check if medium already exists
        if (existingSlugMap.has(slug)) {
          skippedList.push({
            row: rowNumber,
            reason: "Medium already exists",
            data: row,
          });
          return;
        }

        // Extract other optional fields
        const description =
          (
            row.Description ||
            row.description ||
            row.DESCRIPTION ||
            row.Desc ||
            row.desc ||
            row.DESC
          )
            ?.toString()
            .trim() || null;

        const status =
          (row.Status || row.status || row.STATUS)
            ?.toString()
            .trim()
            .toLowerCase() || "active";

        // Validate status
        if (!["active", "inactive"].includes(status)) {
          errorLog.push({
            row: rowNumber,
            reason: "Status must be one of: active, inactive",
            data: row,
          });
          return;
        }

        // Prepare medium data for insertion
        const mediumData = {
          name: name,
          slug: slug,
          code: slug,
          description: description,
          status: status,
          createdBy: userId || null,
        };

        insertList.push(mediumData);
        // Add to existing map to prevent duplicates within the same file
        existingSlugMap.set(slug, { name: name, slug: slug });
      } catch (error) {
        errorLog.push({
          row: rowNumber,
          reason: `Processing error: ${error.message}`,
          data: row,
        });
      }
    });

    // Perform bulk insert using transaction
    let insertedMediums = [];
    if (insertList.length > 0) {
      const transaction = await db.sequelize.transaction();
      try {
        insertedMediums = await db.Medium.bulkCreate(insertList, {
          transaction,
          validate: true,
        });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw new Error(`Bulk insert failed: ${error.message}`);
      }
    }

    // Create rejected rows file (skipped + errors) with reasons
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return standardized results for consistent frontend handling
    return {
      totalRows: rows.length,
      insertedCount: insertedMediums.length,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertedMediums,
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    // Create a map of rejected rows by row number for quick lookup
    const rejectedRowsMap = new Map();
    rejectedRows.forEach((rejected) => {
      rejectedRowsMap.set(rejected.row, rejected.reason);
    });

    // Filter original rows to get only rejected ones and add reason column
    const rejectedRowsWithReasons = [];

    originalRows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)
      if (rejectedRowsMap.has(rowNumber)) {
        // Add the reason column to the original row data
        const rowWithReason = {
          ...row,
          "Rejection Reason": rejectedRowsMap.get(rowNumber),
        };
        rejectedRowsWithReasons.push(rowWithReason);
      }
    });

    if (rejectedRowsWithReasons.length === 0) {
      return null;
    }

    // Create new workbook with rejected rows
    const newWorkbook = xlsx.utils.book_new();
    const newWorksheet = xlsx.utils.json_to_sheet(rejectedRowsWithReasons);
    xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, "Rejected Rows");

    // Generate filename for rejected rows file
    const originalFileName = path.basename(
      originalFilePath,
      path.extname(originalFilePath)
    );
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const rejectedFileName = `${originalFileName}_rejected_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Write the rejected rows file
    xlsx.writeFile(newWorkbook, rejectedFilePath);

    // Return the download URL (relative to uploads directory)
    const uploadsDir = path.join(__basedir, "uploads");
    const relativePath = path.relative(uploadsDir, rejectedFilePath);
    return `/uploads/${relativePath.replace(/\\/g, "/")}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// helper functions
async function getSingleRecord(id) {
  const record = await db.Medium.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
