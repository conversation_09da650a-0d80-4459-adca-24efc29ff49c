const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const variantService = require("./variant.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/variants";
    cb(null, __basedir + "uploads/variants");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", variantSchema, create);
router.post("/insert-variants", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", variantSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  variantService
    .create(req.body)
    .then((variant) => {
      logRequest(
        req,
        `Created a new Variant with name: "${variant?.name}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  variantService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Variants", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  variantService
    .getById(req.params.id)
    .then((variant) => {
      logRequest(req, `Fetched Variant with name: "${variant?.name}"`, "READ");
      res.json(variant);
    })
    .catch(next);
}

function variantSchema(req, res, next) {
  const schema = Joi.object({
    propertyId: Joi.number().integer().required(),
    name: Joi.string().required(),
    slug: Joi.string().optional(), // Auto-generated, so optional
    code: Joi.string().optional(), // Auto-generated, so optional
    description: Joi.string().optional(),
    status: Joi.string().valid("active", "inactive").optional(),
    createdBy: Joi.number().integer().optional(),
    updatedBy: Joi.number().integer().optional(),
    deletedBy: Joi.number().integer().optional(),
    deletedAt: Joi.date().optional(),
    userId: Joi.number().optional(), // For audit tracking
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  variantService
    .update(req.params.id, req.body)
    .then((variant) => {
      logRequest(
        req,
        `Updated Variant with name: "${variant?.name}" and status: "${variant?.status}"`,
        "UPDATE"
      );
      res.json(variant);
    })
    .catch(next);
}

function _delete(req, res, next) {
  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;

  variantService
    .delete(req.params.id, userId)
    .then(() => {
      logRequest(req, `Deleted Variant with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  variantService
    .bulkImport(filePath, userId)
    .then((result) => {
      logRequest(
        req,
        `Bulk imported variants: ${result.insertedCount} inserted, ${result.skippedCount} skipped, ${result.errorCount} errors`,
        "BULK_IMPORT"
      );

      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}
