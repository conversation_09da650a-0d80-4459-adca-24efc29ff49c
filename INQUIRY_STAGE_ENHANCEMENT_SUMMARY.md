# Inquiry Stage Enhancement Summary

## Overview
Enhanced the Transaction Bulk Import functionality to support a special "Inquiry" stage with minimal validation requirements.

## Business Requirements Implemented

### For "Inquiry" Stage:
1. **Mandatory Fields**: Only `Contact Name` and (`Mobile` OR `Email`) are required
2. **Optional Fields**: All other fields are optional (Company Name, Medium, Channel, Property, etc.)
3. **No Validation**: If additional data is provided, insert as-is without validation
4. **Error Handling**: Throw error only if both mobile and email are missing

### For All Other Stages:
- **Existing Flow**: Keep all existing validation logic unchanged
- **"Paid Customer"**: All master fields remain mandatory
- **Other Stages**: Existing validation rules apply

## Technical Changes Made

### File: `app/transactions/transaction.service.js`

#### A. Early Stage Detection and Validation Setup
**Lines Modified**: 407-444
```javascript
// Extract stage early to determine validation requirements
const stage = (row.Stage || "").toString().trim();
const isPaidCustomer = stage === "Paid Customer";
const isInquiry = stage === "Inquiry";

// Validate required fields based on stage
if (!contactName) {
  errorLog.push({
    row: rowNumber,
    data: row,
    reason: "Missing required field: Contact Name",
  });
  continue;
}

// For Inquiry stage: only Contact Name and (Mobile OR Email) are mandatory
// For other stages: existing validation applies
if (isInquiry) {
  if (!mobile && !emailId) {
    errorLog.push({
      row: rowNumber,
      data: row,
      reason: "For Inquiry stage: Either Mobile or Email ID is required",
    });
    continue;
  }
} else {
  // Existing validation for non-Inquiry stages
  if (!mobile && !emailId) {
    errorLog.push({
      row: rowNumber,
      data: row,
      reason: "Missing required field: Either Mobile or Email ID",
    });
    continue;
  }
}
```

#### B. Optional Company Name for Inquiry Stage
**Lines Modified**: 464-478
```javascript
// Step 2: Extract company information
const companyName = (row["Company Name"] || row.CompanyName || "")
  .toString()
  .trim();

// For Inquiry stage: Company Name is optional
// For other stages: Company Name is required
if (!isInquiry && !companyName) {
  errorLog.push({
    row: rowNumber,
    data: row,
    reason: "Missing required field: Company Name",
  });
  continue;
}
```

#### C. Optional Customer Creation for Inquiry Stage
**Lines Modified**: 480-489
```javascript
// Step 3: Find or create customer (skip for Inquiry stage if no company name)
let customerId = null;

if (companyName) {
  const customerSlug = utils.generateSlug(companyName);
  let customer = customerMap.get(customerSlug);

  if (customer) {
    customerId = customer.id;
  } else {
    // ... customer creation logic
  }
}
```

#### D. Customer Employee Creation with Null Customer ID
**Lines Modified**: 572, 577-585
```javascript
// Step 4: Create customer employee if not found
if (!customerEmployee && (customerId || isInquiry)) {
  // ... 
  const newCustomerEmployee = await db.CustomerEmployee.create({
    customerId: customerId || null, // Allow null for Inquiry stage
    name: contactName,
    email: emailId || null,
    phone: mobile || null,
    designation: designation || null,
    status: "active",
    createdBy: userId || null,
  });
}
```

## Validation Logic Summary

### "Inquiry" Stage Validation:
```
✅ Contact Name (mandatory)
✅ Mobile OR Email (mandatory - at least one required)
❌ Company Name (optional)
❌ Medium (optional)
❌ Channel (optional) 
❌ Property (optional)
❌ All other fields (optional)
```

### "Paid Customer" Stage Validation:
```
✅ Contact Name (mandatory)
✅ Mobile OR Email (mandatory)
✅ Company Name (mandatory)
✅ Medium (mandatory)
✅ Channel (mandatory)
✅ Property (mandatory)
❌ Other fields (optional)
```

### Other Stages Validation:
```
✅ Contact Name (mandatory)
✅ Mobile OR Email (mandatory)
✅ Company Name (mandatory)
❌ Medium (optional)
❌ Channel (optional)
❌ Property (optional)
❌ Other fields (optional)
```

## Data Handling for "Inquiry" Stage

### Minimal Data Scenario:
```excel
Contact Name | Mobile      | Email           | Stage
John Doe     | 9876543210  |                | Inquiry
Jane Smith   |             | <EMAIL>  | Inquiry
```

### Rich Data Scenario:
```excel
Contact Name | Mobile      | Email           | Company Name | Medium   | Stage
John Doe     | 9876543210  | <EMAIL> | ABC Corp     | Digital  | Inquiry
```

Both scenarios will be accepted and processed successfully.

## Database Impact

### Customer Employee Records:
- **With Company**: `customerId` will be populated with actual customer ID
- **Without Company**: `customerId` will be `null` for Inquiry stage
- **Contact Info**: Name, email, phone will always be populated

### Transaction Records:
- **Master Fields**: Can be `null` for Inquiry stage if not provided
- **Contact Link**: Always linked to customer employee record
- **Stage Field**: Will contain "Inquiry" for proper identification

## Error Messages

### New Error Messages for Inquiry Stage:
- `"For Inquiry stage: Either Mobile or Email ID is required"`

### Existing Error Messages (unchanged):
- `"Missing required field: Contact Name"`
- `"Missing required field: Company Name"` (only for non-Inquiry stages)
- `"Missing required fields for Paid Customer: Medium, Channel, Property"`

## Benefits

1. **Lead Capture**: Can capture basic inquiry information with minimal data
2. **Progressive Enhancement**: Can add more details later as lead progresses
3. **Flexible Import**: Supports both minimal and rich data scenarios
4. **Data Integrity**: Maintains referential integrity with null handling
5. **Backward Compatibility**: No impact on existing stage processing

## Testing Scenarios

### Test Case 1: Minimal Inquiry Data
```excel
Contact Name | Mobile      | Stage
John Doe     | 9876543210  | Inquiry
```
**Expected**: ✅ Success - Creates customer employee with null customer

### Test Case 2: Inquiry with Missing Contact Info
```excel
Contact Name | Stage
John Doe     | Inquiry
```
**Expected**: ❌ Error - "For Inquiry stage: Either Mobile or Email ID is required"

### Test Case 3: Inquiry with Rich Data
```excel
Contact Name | Mobile      | Company Name | Medium   | Channel | Stage
John Doe     | 9876543210  | ABC Corp     | Digital  | Google  | Inquiry
```
**Expected**: ✅ Success - Creates all entities and relationships

### Test Case 4: Paid Customer (unchanged)
```excel
Contact Name | Mobile      | Company Name | Medium   | Channel | Property | Stage
John Doe     | 9876543210  | ABC Corp     | Digital  | Google  | Website  | Paid Customer
```
**Expected**: ✅ Success - All validations apply as before

## Impact Assessment

- **Zero Breaking Changes**: Existing functionality completely preserved
- **Enhanced Flexibility**: Supports lead capture at inquiry stage
- **Data Quality**: Maintains data integrity with proper null handling
- **User Experience**: Reduces friction for initial lead import
- **Scalability**: Easy to extend for other minimal-validation stages
