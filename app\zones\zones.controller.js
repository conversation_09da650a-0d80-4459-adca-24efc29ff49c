const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const zoneService = require("./zone.service");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/cities";
    cb(null, __basedir + "uploads/cities");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Routes
router.get("/", getAll);
router.post("/", uploadConfig.single("file"), zoneSchema, create);
router.post("/insert-cities", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", zoneSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  zoneService
    .create(req.body)
    .then(() => {
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  zoneService
    .getAll(req.query)
    .then((records) => {
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  zoneService
    .getById(req.params.id)
    .then((zone) => {
      res.json(zone);
    })
    .catch(next);
}

function zoneSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    slug: Joi.string().optional(), // Auto-generated, so optional
    status: Joi.string().valid("active", "inactive").optional(),
    createdBy: Joi.number().integer().optional(),
    updatedBy: Joi.number().integer().optional(),
    deletedBy: Joi.number().integer().optional(),
    deletedAt: Joi.date().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  zoneService
    .update(req.params.id, req.body)
    .then((zone) => {
      res.json(zone);
    })
    .catch(next);
}

function _delete(req, res, next) {
  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;

  zoneService
    .delete(req.params.id, userId)
    .then(() => {
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  zoneService
    .bulkImport(filePath, userId)
    .then((result) => {
      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      next(error);
    });
}
