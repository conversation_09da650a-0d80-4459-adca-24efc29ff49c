const config = require("../../config.json");
const mysql = require("mysql2/promise");
const { Sequelize } = require("sequelize");

module.exports = db = {};

initialize();
async function initialize() {
  // create db if it doesn't already exist
  const environment = config.environment;
  let databaseValue = null;

  if (environment === "UAT") {
    databaseValue = config.fatDatabase;
  } else if (environment === "PROD") {
    databaseValue = config.prodDatabase;
  } else {
    databaseValue = config.database;
  }
  const { host, port, user, password, database } = databaseValue;
  // connect to dbs
  const sequelize = new Sequelize(database, user, password, {
    host: host,
    port: port,
    dialect: "mysql",
    pool: {
      max: 100,
      min: 0,
      idle: 5000,
      evict: 10000,
    },
  });

  sequelize
    .authenticate()
    .then(() => {
      console.log("Connection has been established successfully.");
    })
    .catch((error) => {
      console.error("Unable to connect to the database: ", error);
    });

  // init models and add them to the exported db object

  db.Contact = require("../contacts/contact.model")(sequelize);
  db.Channel = require("../channels/channel.model")(sequelize);
  db.City = require("../cities/city.model")(sequelize);
  db.Company = require("../companies/company.model")(sequelize);
  db.Customer = require("../customers/customer.model")(sequelize);
  db.CustomerEmployee =
    require("../customer-employees/customer-employee.model")(sequelize);
  db.Medium = require("../mediums/medium.model")(sequelize);
  db.Property = require("../properties/property.model")(sequelize);
  db.Transaction = require("../transactions/transaction.model")(sequelize);
  db.User = require("../users/user.model")(sequelize);
  db.Variant = require("../variants/variant.model")(sequelize);
  db.Stage = require("../stages/stage.model")(sequelize);
  db.CompanyCategory = require("../company-categories/company-category.model")(
    sequelize
  );
  db.ContactStatus = require("../contact-statuses/contact-status.model")(
    sequelize
  );
  db.State = require("../states/state.model")(sequelize);
  db.Zone = require("../zones/zone.model")(sequelize);

  // Define associations
  // Transaction belongs to Customer, Medium, Channel, Property, Variant, City
  db.Transaction.belongsTo(db.Customer, {
    foreignKey: "customerId",
    as: "customerInfo",
  });
  db.Transaction.belongsTo(db.CustomerEmployee, {
    foreignKey: "customerEmployeeId",
    as: "customerEmployeeInfo",
  });
  db.Transaction.belongsTo(db.Medium, {
    foreignKey: "mediumId",
    as: "mediumInfo",
  });
  db.Transaction.belongsTo(db.Channel, {
    foreignKey: "channelId",
    as: "channelInfo",
  });
  db.Transaction.belongsTo(db.Property, {
    foreignKey: "propertyId",
    as: "propertyInfo",
  });
  db.Transaction.belongsTo(db.Variant, {
    foreignKey: "variantId",
    as: "variantInfo",
  });
  db.Transaction.belongsTo(db.City, {
    foreignKey: "cityId",
    as: "cityInfo",
  });
  db.Transaction.belongsTo(db.State, {
    foreignKey: "stateId",
    as: "stateInfo",
  });
  db.Transaction.belongsTo(db.Zone, {
    foreignKey: "zoneId",
    as: "zoneInfo",
  });

  // Customer has many transactions
  db.Customer.hasMany(db.Transaction, {
    foreignKey: "customerId",
    as: "transactions",
  });

  // Other associations for completeness
  db.Medium.hasMany(db.Transaction, {
    foreignKey: "mediumId",
    as: "transactions",
  });
  db.Channel.hasMany(db.Transaction, {
    foreignKey: "channelId",
    as: "transactions",
  });
  db.Property.hasMany(db.Transaction, {
    foreignKey: "propertyId",
    as: "transactions",
  });
  db.Variant.hasMany(db.Transaction, {
    foreignKey: "variantId",
    as: "transactions",
  });
  db.City.hasMany(db.Transaction, {
    foreignKey: "cityId",
    as: "transactions",
  });

  // Customer belongs to City
  db.Customer.belongsTo(db.City, {
    foreignKey: "cityId",
    as: "cityInfo",
  });

  // Customer belongs to CompanyCategory
  db.Customer.belongsTo(db.CompanyCategory, {
    foreignKey: "companyCategoryId",
    as: "companyCategoryInfo",
  });

  // Customer has many CustomerEmployees
  db.Customer.hasMany(db.CustomerEmployee, {
    foreignKey: "customerId",
    as: "employees",
  });

  // CustomerEmployee belongs to Customer
  db.CustomerEmployee.belongsTo(db.Customer, {
    foreignKey: "customerId",
    as: "customerInfo",
  });

  // Property belongs to Channel and Medium
  db.Property.belongsTo(db.Channel, {
    foreignKey: "channelId",
    as: "channelInfo",
  });
  db.Property.belongsTo(db.Medium, {
    foreignKey: "mediumId",
    as: "mediumInfo",
  });

  // sync all models with database
  await sequelize.sync();

  db.sequelize = sequelize;
}
