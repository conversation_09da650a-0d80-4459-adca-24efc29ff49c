const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const cityService = require("./cities.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/cities";
    cb(null, __basedir + "uploads/cities");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", uploadConfig.single("file"), citySchema, create);
router.post("/insert-cities", uploadConfig.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", citySchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  cityService
    .create(req.body)
    .then((city) => {
      logRequest(
        req,
        `Created a new City with name: "${city?.name}" and status: "${city?.status}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  cityService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all Cities", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  cityService
    .getById(req.params.id)
    .then((city) => {
      logRequest(req, `Fetched City with name: "${city?.name}"`, "READ");
      res.json(city);
    })
    .catch(next);
}

function citySchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    slug: Joi.string().optional(), // Auto-generated, so optional
    code: Joi.string().optional(), // Auto-generated, so optional
    state: Joi.string().optional(),
    country: Joi.string().optional(),
    status: Joi.string().valid("active", "inactive").optional(),
    createdBy: Joi.number().integer().optional(),
    updatedBy: Joi.number().integer().optional(),
    deletedBy: Joi.number().integer().optional(),
    deletedAt: Joi.date().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  // Add userId from headers for audit tracking
  req.body.userId = req?.headers?.userid;

  cityService
    .update(req.params.id, req.body)
    .then((city) => {
      logRequest(
        req,
        `Updated City with name: "${city?.name}" and status: "${city?.status}"`,
        "UPDATE"
      );
      res.json(city);
    })
    .catch(next);
}

function _delete(req, res, next) {
  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;

  cityService
    .delete(req.params.id, userId)
    .then(() => {
      logRequest(req, `Deleted City with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  cityService
    .bulkImport(filePath, userId)
    .then((result) => {
      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      logRequest(
        req,
        `Bulk import failed: ${error.message}`,
        "BULK_IMPORT_ERROR"
      );
      next(error);
    });
}
