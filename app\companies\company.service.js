const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  const whereClause = {
    deletedAt: null, // Exclude soft-deleted records
  };

  // Add additional filters if provided
  if (params?.status) {
    whereClause.status = params.status;
  }
  if (params?.type) {
    whereClause.type = params.type;
  }
  if (params?.industry) {
    whereClause.industry = params.industry;
  }

  return await db.Company.findAll({
    where: whereClause,
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Generate slug from name
  params.slug = utils.generateSlug(params.name);
  // Generate code from name
  params.code = utils.generateSlug(params.name);
  // Set default status if not provided
  if (!params.status) params.status = "active";
  // Set audit fields
  params.createdBy = params.userId || null;

  const record = await db.Company.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Update slug and code if name is changed
  if (params.name && params.name !== record.name) {
    params.slug = utils.generateSlug(params.name);
    params.code = utils.generateSlug(params.name);
  }

  // Set audit fields
  params.updatedBy = params.userId || null;

  // copy params to Company and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id, userId) {
  const record = await getSingleRecord(id);

  // Soft delete
  record.deletedBy = userId || null;
  record.deletedAt = new Date();
  await record.save();

  // Or hard delete if preferred
  // await record.destroy();
}

async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Collect company names from Excel and generate slugs
    const companyNamesInSheet = rows
      .map(
        (row) =>
          row.Name ||
          row.name ||
          row.CompanyName ||
          row["Company Name"] ||
          row.company_name ||
          row.COMPANY_NAME
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const slugsInSheet = companyNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Get existing companies with matching slugs
    const existingCompanies = await db.Company.findAll({
      where: {
        slug: slugsInSheet,
        deletedAt: null,
      },
      attributes: ["id", "name", "slug"],
    });

    // Create a map for quick lookup of existing companies
    const existingSlugMap = new Map();
    existingCompanies.forEach((company) => {
      existingSlugMap.set(company.slug, company);
    });

    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Process each row
    rows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)

      try {
        // Extract company data with multiple column name variations
        const name = (
          row.Name ||
          row.name ||
          row.CompanyName ||
          row["Company Name"] ||
          row.company_name ||
          row.COMPANY_NAME
        )
          ?.toString()
          .trim();

        const email = (
          row.Email ||
          row.email ||
          row.EMAIL ||
          row["Email Address"] ||
          row.email_address ||
          row.EMAIL_ADDRESS
        )
          ?.toString()
          .trim();

        const type = (
          row.Type ||
          row.type ||
          row.TYPE ||
          row.CompanyType ||
          row["Company Type"] ||
          row.company_type
        )
          ?.toString()
          .trim()
          .toLowerCase();

        // Validate required fields
        if (!name) {
          errorLog.push({
            row: rowNumber,
            reason: "Company name is required",
            data: row,
          });
          return;
        }

        if (!email) {
          errorLog.push({
            row: rowNumber,
            reason: "Email is required",
            data: row,
          });
          return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          errorLog.push({
            row: rowNumber,
            reason: "Invalid email format",
            data: row,
          });
          return;
        }

        if (!type || !["client", "vendor", "partner"].includes(type)) {
          errorLog.push({
            row: rowNumber,
            reason: "Type must be one of: client, vendor, partner",
            data: row,
          });
          return;
        }

        const slug = utils.generateSlug(name);

        // Check if company already exists
        if (existingSlugMap.has(slug)) {
          skippedList.push({
            row: rowNumber,
            reason: "Company already exists",
            data: row,
          });
          return;
        }

        // Extract other optional fields
        const phone =
          (
            row.Phone ||
            row.phone ||
            row.PHONE ||
            row["Phone Number"] ||
            row.phone_number ||
            row.PHONE_NUMBER
          )
            ?.toString()
            .trim() || null;

        const website =
          (row.Website || row.website || row.WEBSITE || row.URL || row.url)
            ?.toString()
            .trim() || null;

        const address =
          (row.Address || row.address || row.ADDRESS)?.toString().trim() ||
          null;

        const city =
          (row.City || row.city || row.CITY)?.toString().trim() || null;

        const state =
          (row.State || row.state || row.STATE)?.toString().trim() || null;

        const country =
          (row.Country || row.country || row.COUNTRY)?.toString().trim() ||
          "India";

        const industry =
          (row.Industry || row.industry || row.INDUSTRY)?.toString().trim() ||
          null;

        const taxId =
          (
            row.TaxId ||
            row.taxId ||
            row.tax_id ||
            row.TAX_ID ||
            row["Tax ID"] ||
            row.GST ||
            row.gst
          )
            ?.toString()
            .trim() || null;

        const registrationNumber =
          (
            row.RegistrationNumber ||
            row.registrationNumber ||
            row.registration_number ||
            row.REGISTRATION_NUMBER ||
            row["Registration Number"] ||
            row.RegNo ||
            row.reg_no
          )
            ?.toString()
            .trim() || null;

        const status =
          (row.Status || row.status || row.STATUS)
            ?.toString()
            .trim()
            .toLowerCase() || "active";

        // Validate status
        if (!["active", "inactive", "pending"].includes(status)) {
          errorLog.push({
            row: rowNumber,
            reason: "Status must be one of: active, inactive, pending",
            data: row,
          });
          return;
        }

        const notes =
          (
            row.Notes ||
            row.notes ||
            row.NOTES ||
            row.Description ||
            row.description ||
            row.DESCRIPTION
          )
            ?.toString()
            .trim() || null;

        // Prepare company data for insertion
        const companyData = {
          name: name,
          slug: slug,
          code: slug,
          email: email,
          phone: phone,
          website: website,
          address: address,
          city: city,
          state: state,
          country: country,
          type: type,
          industry: industry,
          taxId: taxId,
          registrationNumber: registrationNumber,
          status: status,
          notes: notes,
          createdBy: userId || null,
        };

        insertList.push(companyData);
        // Add to existing map to prevent duplicates within the same file
        existingSlugMap.set(slug, { name: name, slug: slug });
      } catch (error) {
        errorLog.push({
          row: rowNumber,
          reason: `Processing error: ${error.message}`,
          data: row,
        });
      }
    });

    // Perform bulk insert using transaction
    let insertedCompanies = [];
    if (insertList.length > 0) {
      const transaction = await db.sequelize.transaction();
      try {
        insertedCompanies = await db.Company.bulkCreate(insertList, {
          transaction,
          validate: true,
        });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw new Error(`Bulk insert failed: ${error.message}`);
      }
    }

    // Create rejected rows file (skipped + errors) with reasons
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return standardized results for consistent frontend handling
    return {
      totalRows: rows.length,
      insertedCount: insertedCompanies.length,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertedCompanies,
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    // Create a map of rejected rows by row number for quick lookup
    const rejectedRowsMap = new Map();
    rejectedRows.forEach((rejected) => {
      rejectedRowsMap.set(rejected.row, rejected.reason);
    });

    // Filter original rows to get only rejected ones and add reason column
    const rejectedRowsWithReasons = [];

    originalRows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)
      if (rejectedRowsMap.has(rowNumber)) {
        // Add the reason column to the original row data
        const rowWithReason = {
          ...row,
          "Rejection Reason": rejectedRowsMap.get(rowNumber),
        };
        rejectedRowsWithReasons.push(rowWithReason);
      }
    });

    if (rejectedRowsWithReasons.length === 0) {
      return null;
    }

    // Create new workbook with rejected rows
    const newWorkbook = xlsx.utils.book_new();
    const newWorksheet = xlsx.utils.json_to_sheet(rejectedRowsWithReasons);
    xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, "Rejected Rows");

    // Generate filename for rejected rows file
    const originalFileName = path.basename(
      originalFilePath,
      path.extname(originalFilePath)
    );
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const rejectedFileName = `${originalFileName}_rejected_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Write the rejected rows file
    xlsx.writeFile(newWorkbook, rejectedFilePath);

    // Return the download URL (relative to uploads directory)
    const uploadsDir = path.join(__basedir, "uploads");
    const relativePath = path.relative(uploadsDir, rejectedFilePath);
    return `/uploads/${relativePath.replace(/\\/g, "/")}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Company.findOne({
    where: { id, deletedAt: null },
  });
  if (!record) throw "Company not found";
  return record;
}
